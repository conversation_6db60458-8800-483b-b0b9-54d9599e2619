import React, { useState, useMemo } from 'react';
import { Download, Search, ArrowLeft, Edit3, Check, X } from 'lucide-react';
import type {
  Student,
  Section,
  Quarter,
  QuarterlyGrade,
  StudentGradeRecord,
} from '../types';
import { getGradeDescriptor } from '../types';
import {
  mockStudents,
  mockGradeRecords,
  canTeacherEditGrades,
} from '../data/mockData';

interface GradeManagementProps {
  teacherId: string;
  selectedSection: Section;
  currentQuarter: Quarter;
  onBack?: () => void;
}

const GradeManagement: React.FC<GradeManagementProps> = ({
  teacherId,
  selectedSection,
  currentQuarter,
  onBack,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [editingStudentId, setEditingStudentId] = useState<string | null>(null);
  const [editingGrade, setEditingGrade] = useState<string>('');

  // Get students for the selected section
  const sectionStudents = useMemo(() => {
    return mockStudents.filter(
      (student) => student.section === selectedSection.name
    );
  }, [selectedSection.name]);

  // Filter students based on search
  const filteredStudents = useMemo(() => {
    return sectionStudents.filter(
      (student) =>
        student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.studentId.includes(searchTerm)
    );
  }, [sectionStudents, searchTerm]);

  // Check if teacher can edit grades for this section
  const canEdit = canTeacherEditGrades(teacherId, '1', selectedSection.id);

  const getStudentGradeStatus = (student: Student) => {
    const gradeRecord = mockGradeRecords.find(
      (record) =>
        record.studentId === student.studentId && record.subjectId === '1'
    );

    if (!gradeRecord) {
      return { status: 'missing', grade: null, color: 'text-gray-500' };
    }

    // Check current quarter grade first
    const quarterGrade = gradeRecord.quarters[currentQuarter];
    if (quarterGrade && quarterGrade.isComplete) {
      const grade = quarterGrade.computedGrade;
      let color = 'text-gray-600';
      if (grade >= 90) color = 'text-green-600';
      else if (grade >= 85) color = 'text-blue-600';
      else if (grade >= 80) color = 'text-yellow-600';
      else if (grade >= 75) color = 'text-orange-600';
      else color = 'text-red-600';

      return {
        status: 'complete',
        grade: grade.toFixed(1),
        descriptor: getGradeDescriptor(grade),
        color,
      };
    }

    // Fall back to final grade if no current quarter grade
    if (gradeRecord.finalGrade) {
      const grade = gradeRecord.finalGrade;
      let color = 'text-gray-600';
      if (grade >= 90) color = 'text-green-600';
      else if (grade >= 85) color = 'text-blue-600';
      else if (grade >= 80) color = 'text-yellow-600';
      else if (grade >= 75) color = 'text-orange-600';
      else color = 'text-red-600';

      return {
        status: 'complete',
        grade: grade.toFixed(1),
        descriptor: getGradeDescriptor(grade),
        color,
      };
    }

    // Check if there's an incomplete quarter grade
    if (quarterGrade && !quarterGrade.isComplete) {
      return { status: 'incomplete', grade: null, color: 'text-yellow-600' };
    }

    return { status: 'missing', grade: null, color: 'text-gray-500' };
  };

  const handleEditClick = (student: Student, e: React.MouseEvent) => {
    e.stopPropagation();
    const gradeRecord = mockGradeRecords.find(
      (record) =>
        record.studentId === student.studentId && record.subjectId === '1'
    );

    let currentGrade = '';

    // Check current quarter grade first
    const quarterGrade = gradeRecord?.quarters[currentQuarter];
    if (quarterGrade && quarterGrade.isComplete) {
      currentGrade = quarterGrade.computedGrade.toString();
    } else if (gradeRecord?.finalGrade) {
      // Fall back to final grade
      currentGrade = gradeRecord.finalGrade.toString();
    }

    setEditingStudentId(student.studentId);
    setEditingGrade(currentGrade);
  };

  const handleSaveGrade = (studentId: string) => {
    const gradeValue = parseFloat(editingGrade);

    if (isNaN(gradeValue) || gradeValue < 0 || gradeValue > 100) {
      return; // Invalid grade
    }

    // Create quarterly grade object
    const quarterlyGrade: QuarterlyGrade = {
      studentId,
      subjectId: '1',
      quarter: currentQuarter,
      components: {
        writtenWorks: [],
        performanceTasks: [],
        quarterlyAssessment: 0,
      },
      computedGrade: gradeValue,
      isComplete: true,
      dateRecorded: new Date(),
    };

    // Update local mock data (for demo purposes)
    const existingRecordIndex = mockGradeRecords.findIndex(
      (record) => record.studentId === studentId && record.subjectId === '1'
    );

    if (existingRecordIndex >= 0) {
      mockGradeRecords[existingRecordIndex].quarters[currentQuarter] =
        quarterlyGrade;
    } else {
      // Create new grade record
      const newRecord: StudentGradeRecord = {
        studentId,
        subjectId: '1',
        quarters: { [currentQuarter]: quarterlyGrade },
      };
      mockGradeRecords.push(newRecord);
    }

    setEditingStudentId(null);
    setEditingGrade('');
  };

  const handleCancelEdit = () => {
    setEditingStudentId(null);
    setEditingGrade('');
  };

  if (!canEdit) {
    return (
      <div className="p-6 bg-gray-50 min-h-screen">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h2 className="text-lg font-semibold text-red-900 mb-2">
            Access Denied
          </h2>
          <p className="text-red-800">
            You don't have permission to edit grades for this section and
            subject.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex items-center space-x-4 mb-6">
        {onBack && (
          <button
            onClick={onBack}
            className="w-10 h-10 text-white rounded-lg flex items-center justify-center hover:opacity-80 transition-opacity bg-custom-green"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
        )}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Grade Management</h1>
          <p className="text-gray-600">
            {selectedSection.name} • {selectedSection.subject} •{' '}
            {currentQuarter}
          </p>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search students..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-custom-green focus:border-custom-green"
            />
          </div>

          {/* Export Button */}
          <button className="flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
            <Download className="w-4 h-4" />
            <span>Export</span>
          </button>
        </div>
      </div>

      {/* Students Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {/* Table Header */}
        <div className="bg-custom-green text-white">
          <div className="grid grid-cols-3 gap-4 px-6 py-3">
            <div className="font-medium text-sm">Student</div>
            <div className="font-medium text-sm">Student ID</div>
            <div className="font-medium text-sm">Grade</div>
          </div>
        </div>

        {/* Table Body */}
        <div className="divide-y divide-gray-200">
          {filteredStudents.length === 0 ? (
            <div className="px-6 py-8 text-center text-gray-500">
              No students found matching your criteria
            </div>
          ) : (
            filteredStudents.map((student) => {
              const gradeStatus = getStudentGradeStatus(student);
              const isEditing = editingStudentId === student.studentId;

              return (
                <div
                  key={student.id}
                  className="grid grid-cols-3 gap-4 px-6 py-4 hover:bg-gray-50"
                >
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {student.name}
                    </div>
                  </div>
                  <div className="text-sm text-gray-600">
                    {student.studentId}
                  </div>
                  <div className="flex items-center space-x-2">
                    {isEditing ? (
                      <>
                        <input
                          type="number"
                          min="0"
                          max="100"
                          step="0.1"
                          value={editingGrade}
                          onChange={(e) => setEditingGrade(e.target.value)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              handleSaveGrade(student.studentId);
                            } else if (e.key === 'Escape') {
                              handleCancelEdit();
                            }
                          }}
                          className="w-20 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-custom-green focus:border-custom-green"
                          placeholder="0-100"
                          autoFocus
                        />
                        <button
                          onClick={() => handleSaveGrade(student.studentId)}
                          className="p-1 text-green-600 hover:text-green-700"
                          title="Save"
                        >
                          <Check className="w-4 h-4" />
                        </button>
                        <button
                          onClick={handleCancelEdit}
                          className="p-1 text-red-600 hover:text-red-700"
                          title="Cancel"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </>
                    ) : (
                      <>
                        <span
                          className={`text-sm font-medium ${gradeStatus.color} min-w-[3rem]`}
                        >
                          {gradeStatus.grade || '—'}
                        </span>
                        <button
                          onClick={(e) => handleEditClick(student, e)}
                          className="p-1 text-gray-400 hover:text-gray-600"
                          title="Edit Grade"
                        >
                          <Edit3 className="w-4 h-4" />
                        </button>
                      </>
                    )}
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>
    </div>
  );
};

export default GradeManagement;
