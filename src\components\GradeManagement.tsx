import React, { useState, useMemo } from 'react';
import { Download, Filter, Search, ArrowLeft } from 'lucide-react';
import type {
  Student,
  Section,
  Quarter,
  QuarterlyGrade,
  StudentGradeRecord,
} from '../types';
import { getGradeDescriptor } from '../types';
import {
  mockStudents,
  mockSections,
  mockGradeRecords,
  canTeacherEditGrades,
} from '../data/mockData';
import GradeEntry from './GradeEntry';

interface GradeManagementProps {
  teacherId: string;
  selectedSection: Section;
  currentQuarter: Quarter;
  onBack?: () => void;
}

const GradeManagement: React.FC<GradeManagementProps> = ({
  teacherId,
  selectedSection,
  currentQuarter,
  onBack,
}) => {
  const [showGradeEntry, setShowGradeEntry] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [editingGrade, setEditingGrade] = useState<QuarterlyGrade | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<
    'all' | 'complete' | 'incomplete' | 'missing'
  >('all');

  // Get students for the selected section
  const sectionStudents = useMemo(() => {
    return mockStudents.filter(
      (student) => student.section === selectedSection.name
    );
  }, [selectedSection.name]);

  // Filter students based on search and status
  const filteredStudents = useMemo(() => {
    let filtered = sectionStudents.filter(
      (student) =>
        student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.studentId.includes(searchTerm)
    );

    if (filterStatus !== 'all') {
      filtered = filtered.filter((student) => {
        const gradeRecord = mockGradeRecords.find(
          (record) =>
            record.studentId === student.studentId && record.subjectId === '1'
        );
        const quarterGrade = gradeRecord?.quarters[currentQuarter];

        switch (filterStatus) {
          case 'complete':
            return quarterGrade && quarterGrade.isComplete;
          case 'incomplete':
            return quarterGrade && !quarterGrade.isComplete;
          case 'missing':
            return !quarterGrade;
          default:
            return true;
        }
      });
    }

    return filtered;
  }, [sectionStudents, searchTerm, filterStatus, currentQuarter]);

  // Check if teacher can edit grades for this section
  const canEdit = canTeacherEditGrades(teacherId, '1', selectedSection.id);

  const getStudentGradeStatus = (student: Student) => {
    const gradeRecord = mockGradeRecords.find(
      (record) =>
        record.studentId === student.studentId && record.subjectId === '1'
    );
    const quarterGrade = gradeRecord?.quarters[currentQuarter];

    if (!quarterGrade) {
      return { status: 'missing', grade: null, color: 'text-gray-500' };
    }

    if (!quarterGrade.isComplete) {
      return { status: 'incomplete', grade: null, color: 'text-yellow-600' };
    }

    const grade = quarterGrade.computedGrade;
    let color = 'text-gray-600';
    if (grade >= 90) color = 'text-green-600';
    else if (grade >= 85) color = 'text-blue-600';
    else if (grade >= 80) color = 'text-yellow-600';
    else if (grade >= 75) color = 'text-orange-600';
    else color = 'text-red-600';

    return {
      status: 'complete',
      grade: grade.toFixed(1),
      descriptor: getGradeDescriptor(grade),
      color,
    };
  };

  const handleStudentClick = (student: Student) => {
    const gradeRecord = mockGradeRecords.find(
      (record) =>
        record.studentId === student.studentId && record.subjectId === '1'
    );
    const quarterGrade = gradeRecord?.quarters[currentQuarter];

    setSelectedStudent(student);
    setEditingGrade(quarterGrade || null);
    setShowGradeEntry(true);
  };

  const handleSaveGrade = (grade: QuarterlyGrade) => {
    // In a real app, this would save to the backend
    console.log('Saving grade:', grade);

    // Update local mock data (for demo purposes)
    const existingRecordIndex = mockGradeRecords.findIndex(
      (record) =>
        record.studentId === grade.studentId &&
        record.subjectId === grade.subjectId
    );

    if (existingRecordIndex >= 0) {
      mockGradeRecords[existingRecordIndex].quarters[grade.quarter] = grade;
    } else {
      // Create new grade record
      const newRecord: StudentGradeRecord = {
        studentId: grade.studentId,
        subjectId: grade.subjectId,
        quarters: { [grade.quarter]: grade },
      };
      mockGradeRecords.push(newRecord);
    }

    setShowGradeEntry(false);
    setSelectedStudent(null);
    setEditingGrade(null);
  };

  const handleCancelGradeEntry = () => {
    setShowGradeEntry(false);
    setSelectedStudent(null);
    setEditingGrade(null);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'complete':
        return (
          <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded">
            Complete
          </span>
        );
      case 'incomplete':
        return (
          <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded">
            In Progress
          </span>
        );
      case 'missing':
        return (
          <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded">
            Not Started
          </span>
        );
      default:
        return null;
    }
  };

  const getCompletionStats = () => {
    const total = sectionStudents.length;
    const complete = sectionStudents.filter((student) => {
      const gradeRecord = mockGradeRecords.find(
        (record) =>
          record.studentId === student.studentId && record.subjectId === '1'
      );
      const quarterGrade = gradeRecord?.quarters[currentQuarter];
      return quarterGrade && quarterGrade.isComplete;
    }).length;

    return {
      total,
      complete,
      percentage: total > 0 ? (complete / total) * 100 : 0,
    };
  };

  const stats = getCompletionStats();

  if (!canEdit) {
    return (
      <div className="p-6 bg-gray-50 min-h-screen">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h2 className="text-lg font-semibold text-red-900 mb-2">
            Access Denied
          </h2>
          <p className="text-red-800">
            You don't have permission to edit grades for this section and
            subject.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex items-center space-x-4 mb-6">
        {onBack && (
          <button
            onClick={onBack}
            className="w-10 h-10 text-white rounded-lg flex items-center justify-center hover:opacity-80 transition-opacity bg-custom-green"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
        )}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Grade Management</h1>
          <p className="text-gray-600">
            {selectedSection.name} • {selectedSection.subject} •{' '}
            {currentQuarter}
          </p>
        </div>
      </div>

      {/* Stats Card */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Completion Progress
            </h3>
            <p className="text-sm text-gray-600">
              {stats.complete} of {stats.total} students have complete grades
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-custom-green">
              {stats.percentage.toFixed(0)}%
            </div>
            <div className="w-32 bg-gray-200 rounded-full h-2 mt-2">
              <div
                className="bg-custom-green h-2 rounded-full transition-all duration-300"
                style={{ width: `${stats.percentage}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search students..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-custom-green focus:border-custom-green"
            />
          </div>

          {/* Status Filter */}
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-gray-500" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-custom-green focus:border-custom-green"
            >
              <option value="all">All Students</option>
              <option value="complete">Complete Grades</option>
              <option value="incomplete">In Progress</option>
              <option value="missing">Missing Grades</option>
            </select>
          </div>

          {/* Export Button */}
          <button className="flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
            <Download className="w-4 h-4" />
            <span>Export</span>
          </button>
        </div>
      </div>

      {/* Students Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {/* Table Header */}
        <div className="bg-custom-green text-white">
          <div className="grid grid-cols-5 gap-4 px-6 py-3">
            <div className="font-medium text-sm">Student</div>
            <div className="font-medium text-sm">Student ID</div>
            <div className="font-medium text-sm">Status</div>
            <div className="font-medium text-sm">Grade</div>
            <div className="font-medium text-sm">Descriptor</div>
          </div>
        </div>

        {/* Table Body */}
        <div className="divide-y divide-gray-200">
          {filteredStudents.length === 0 ? (
            <div className="px-6 py-8 text-center text-gray-500">
              No students found matching your criteria
            </div>
          ) : (
            filteredStudents.map((student) => {
              const gradeStatus = getStudentGradeStatus(student);
              return (
                <div
                  key={student.id}
                  className="grid grid-cols-5 gap-4 px-6 py-4 hover:bg-gray-50 cursor-pointer"
                  onClick={() => handleStudentClick(student)}
                >
                  <div>
                    <div className="text-sm font-medium text-blue-600 hover:text-blue-800">
                      {student.name}
                    </div>
                    <div className="text-xs text-gray-500">
                      {student.section}
                    </div>
                  </div>
                  <div className="text-sm text-gray-600">
                    {student.studentId}
                  </div>
                  <div>{getStatusBadge(gradeStatus.status)}</div>
                  <div className={`text-sm font-medium ${gradeStatus.color}`}>
                    {gradeStatus.grade || '—'}
                  </div>
                  <div className="text-sm text-gray-600">
                    {gradeStatus.descriptor || '—'}
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>

      {/* Grade Entry Modal */}
      {showGradeEntry && selectedStudent && (
        <GradeEntry
          student={selectedStudent}
          quarter={currentQuarter}
          subjectId="1"
          subjectName={selectedSection.subject}
          existingGrade={editingGrade}
          onSave={handleSaveGrade}
          onCancel={handleCancelGradeEntry}
          teacherId={teacherId}
        />
      )}
    </div>
  );
};

export default GradeManagement;
