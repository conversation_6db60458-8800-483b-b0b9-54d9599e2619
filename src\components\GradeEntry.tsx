import React, { useState, useEffect } from 'react';
import { Save, X, AlertCircle } from 'lucide-react';
import type { Student, QuarterlyGrade, Quarter } from '../types';
import { getGradeDescriptor } from '../types';

interface GradeEntryProps {
  student: Student;
  quarter: Quarter;
  subjectId: string;
  subjectName: string;
  existingGrade?: QuarterlyGrade;
  onSave: (grade: QuarterlyGrade) => void;
  onCancel: () => void;
  teacherId: string;
}

const GradeEntry: React.FC<GradeEntryProps> = ({
  student,
  quarter,
  subjectId,
  subjectName,
  existingGrade,
  onSave,
  onCancel,
  teacherId,
}) => {
  const [grade, setGrade] = useState<number>(existingGrade?.computedGrade || 0);
  const [error, setError] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Validate grade whenever it changes
  useEffect(() => {
    if (grade < 0 || grade > 100) {
      setError('Grade must be between 0 and 100');
    } else {
      setError('');
    }
  }, [grade]);

  const handleGradeChange = (value: string) => {
    const numValue = parseFloat(value) || 0;
    setGrade(Math.max(0, Math.min(100, numValue)));
  };

  const handleSave = async () => {
    if (error || grade < 0 || grade > 100) {
      return;
    }

    setIsSubmitting(true);

    const quarterlyGrade: QuarterlyGrade = {
      studentId: student.studentId,
      subjectId,
      quarter,
      components: {
        writtenWorks: [],
        performanceTasks: [],
        quarterlyAssessment: 0,
      },
      computedGrade: grade,
      isComplete: true,
      dateRecorded: new Date(),
    };

    try {
      onSave(quarterlyGrade);
    } catch (err) {
      console.error('Error saving grade:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getGradeColor = (grade: number) => {
    if (grade >= 90) return 'text-green-600';
    if (grade >= 85) return 'text-blue-600';
    if (grade >= 80) return 'text-yellow-600';
    if (grade >= 75) return 'text-orange-600';
    return 'text-red-600';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-custom-green text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-bold">Grade Entry</h2>
              <p className="text-green-100 text-sm">
                {student.name} • {quarter}
              </p>
            </div>
            <button
              onClick={onCancel}
              className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-green-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Student Information */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Student ID:</span>
                <p className="text-gray-900">{student.studentId}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Section:</span>
                <p className="text-gray-900">{student.section}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Subject:</span>
                <p className="text-gray-900">{subjectName}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Quarter:</span>
                <p className="text-gray-900">{quarter}</p>
              </div>
            </div>
          </div>

          {/* Grade Input */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Final Grade (0-100)
            </label>
            <input
              type="number"
              min="0"
              max="100"
              step="0.1"
              value={grade}
              onChange={(e) => handleGradeChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-lg font-medium focus:outline-none focus:ring-2 focus:ring-custom-green focus:border-custom-green"
              placeholder="Enter grade (0-100)"
              autoFocus
            />
            {error && (
              <div className="mt-2 flex items-center text-red-600 text-sm">
                <AlertCircle className="w-4 h-4 mr-1" />
                {error}
              </div>
            )}
          </div>

          {/* Grade Preview */}
          {grade > 0 && !error && (
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-2">
                Grade Preview
              </h3>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Final Grade:</span>
                <span className={`text-lg font-bold ${getGradeColor(grade)}`}>
                  {grade.toFixed(1)}
                </span>
              </div>
              <div className="flex items-center justify-between mt-1">
                <span className="text-sm text-gray-600">Descriptor:</span>
                <span className={`text-sm font-medium ${getGradeColor(grade)}`}>
                  {getGradeDescriptor(grade)}
                </span>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-3">
            <button
              onClick={handleSave}
              disabled={!!error || isSubmitting || grade <= 0}
              className="flex-1 bg-custom-green text-white px-4 py-2 rounded-md hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>
                {isSubmitting
                  ? 'Saving...'
                  : existingGrade
                  ? 'Update Grade'
                  : 'Save Grade'}
              </span>
            </button>
            <button
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GradeEntry;
