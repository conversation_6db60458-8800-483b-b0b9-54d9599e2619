import React, { useState, useEffect } from 'react';
import { Save, X, Plus, Trash2, Calculator, AlertCircle } from 'lucide-react';
import type { 
  Student, 
  GradeComponent, 
  QuarterlyGrade, 
  Quarter,
  StudentGradeRecord 
} from '../types';
import { 
  calculateQuarterlyGrade, 
  getGradeDescriptor,
  DEPED_WEIGHTS 
} from '../types';
import { validateGradeComponents } from '../utils/gradingUtils';

interface GradeEntryProps {
  student: Student;
  quarter: Quarter;
  subjectId: string;
  existingGrade?: QuarterlyGrade;
  onSave: (grade: QuarterlyGrade) => void;
  onCancel: () => void;
  teacherId: string;
}

const GradeEntry: React.FC<GradeEntryProps> = ({
  student,
  quarter,
  subjectId,
  existingGrade,
  onSave,
  onCancel,
  teacherId
}) => {
  const [gradeComponents, setGradeComponents] = useState<GradeComponent>({
    writtenWorks: existingGrade?.components.writtenWorks || [0],
    performanceTasks: existingGrade?.components.performanceTasks || [0],
    quarterlyAssessment: existingGrade?.components.quarterlyAssessment || 0
  });

  const [errors, setErrors] = useState<string[]>([]);
  const [computedGrade, setComputedGrade] = useState<number>(0);
  const [isCalculating, setIsCalculating] = useState(false);

  // Calculate grade whenever components change
  useEffect(() => {
    setIsCalculating(true);
    const validationErrors = validateGradeComponents(gradeComponents);
    setErrors(validationErrors);

    if (validationErrors.length === 0) {
      const grade = calculateQuarterlyGrade(gradeComponents);
      setComputedGrade(grade);
    } else {
      setComputedGrade(0);
    }
    setIsCalculating(false);
  }, [gradeComponents]);

  const handleWrittenWorkChange = (index: number, value: string) => {
    const numValue = parseFloat(value) || 0;
    const newWW = [...gradeComponents.writtenWorks];
    newWW[index] = Math.max(0, Math.min(100, numValue));
    setGradeComponents(prev => ({ ...prev, writtenWorks: newWW }));
  };

  const handlePerformanceTaskChange = (index: number, value: string) => {
    const numValue = parseFloat(value) || 0;
    const newPT = [...gradeComponents.performanceTasks];
    newPT[index] = Math.max(0, Math.min(100, numValue));
    setGradeComponents(prev => ({ ...prev, performanceTasks: newPT }));
  };

  const handleQuarterlyAssessmentChange = (value: string) => {
    const numValue = parseFloat(value) || 0;
    setGradeComponents(prev => ({ 
      ...prev, 
      quarterlyAssessment: Math.max(0, Math.min(100, numValue))
    }));
  };

  const addWrittenWork = () => {
    setGradeComponents(prev => ({
      ...prev,
      writtenWorks: [...prev.writtenWorks, 0]
    }));
  };

  const removeWrittenWork = (index: number) => {
    if (gradeComponents.writtenWorks.length > 1) {
      const newWW = gradeComponents.writtenWorks.filter((_, i) => i !== index);
      setGradeComponents(prev => ({ ...prev, writtenWorks: newWW }));
    }
  };

  const addPerformanceTask = () => {
    setGradeComponents(prev => ({
      ...prev,
      performanceTasks: [...prev.performanceTasks, 0]
    }));
  };

  const removePerformanceTask = (index: number) => {
    if (gradeComponents.performanceTasks.length > 1) {
      const newPT = gradeComponents.performanceTasks.filter((_, i) => i !== index);
      setGradeComponents(prev => ({ ...prev, performanceTasks: newPT }));
    }
  };

  const handleSave = () => {
    if (errors.length > 0) {
      return;
    }

    const quarterlyGrade: QuarterlyGrade = {
      studentId: student.studentId,
      subjectId,
      quarter,
      components: gradeComponents,
      computedGrade,
      isComplete: true,
      dateRecorded: new Date()
    };

    onSave(quarterlyGrade);
  };

  const getGradeColor = (grade: number) => {
    if (grade >= 90) return 'text-green-600';
    if (grade >= 85) return 'text-blue-600';
    if (grade >= 80) return 'text-yellow-600';
    if (grade >= 75) return 'text-orange-600';
    return 'text-red-600';
  };

  const calculateAverage = (scores: number[]) => {
    if (scores.length === 0) return 0;
    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-custom-green text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold">Grade Entry</h2>
              <p className="text-green-100">
                {student.name} • {quarter} • Student ID: {student.studentId}
              </p>
            </div>
            <button
              onClick={onCancel}
              className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-green-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* DepEd Grading System Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-blue-900 mb-2">DepEd Grading System</h3>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div className="text-blue-800">
                <span className="font-medium">Written Works:</span> {DEPED_WEIGHTS.writtenWorks * 100}%
              </div>
              <div className="text-blue-800">
                <span className="font-medium">Performance Tasks:</span> {DEPED_WEIGHTS.performanceTasks * 100}%
              </div>
              <div className="text-blue-800">
                <span className="font-medium">Quarterly Assessment:</span> {DEPED_WEIGHTS.quarterlyAssessment * 100}%
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column - Grade Components */}
            <div className="space-y-6">
              {/* Written Works */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Written Works (30%)
                  </h3>
                  <button
                    onClick={addWrittenWork}
                    className="flex items-center space-x-1 text-custom-green hover:text-green-700 text-sm"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Add</span>
                  </button>
                </div>
                <div className="space-y-2">
                  {gradeComponents.writtenWorks.map((score, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <label className="text-sm text-gray-600 w-16">
                        WW {index + 1}:
                      </label>
                      <input
                        type="number"
                        min="0"
                        max="100"
                        value={score}
                        onChange={(e) => handleWrittenWorkChange(index, e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-custom-green focus:border-custom-green"
                        placeholder="0-100"
                      />
                      {gradeComponents.writtenWorks.length > 1 && (
                        <button
                          onClick={() => removeWrittenWork(index)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  ))}
                  <div className="text-sm text-gray-600 mt-2">
                    Average: {calculateAverage(gradeComponents.writtenWorks).toFixed(1)}
                  </div>
                </div>
              </div>

              {/* Performance Tasks */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Performance Tasks (50%)
                  </h3>
                  <button
                    onClick={addPerformanceTask}
                    className="flex items-center space-x-1 text-custom-green hover:text-green-700 text-sm"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Add</span>
                  </button>
                </div>
                <div className="space-y-2">
                  {gradeComponents.performanceTasks.map((score, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <label className="text-sm text-gray-600 w-16">
                        PT {index + 1}:
                      </label>
                      <input
                        type="number"
                        min="0"
                        max="100"
                        value={score}
                        onChange={(e) => handlePerformanceTaskChange(index, e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-custom-green focus:border-custom-green"
                        placeholder="0-100"
                      />
                      {gradeComponents.performanceTasks.length > 1 && (
                        <button
                          onClick={() => removePerformanceTask(index)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  ))}
                  <div className="text-sm text-gray-600 mt-2">
                    Average: {calculateAverage(gradeComponents.performanceTasks).toFixed(1)}
                  </div>
                </div>
              </div>

              {/* Quarterly Assessment */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Quarterly Assessment (20%)
                </h3>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={gradeComponents.quarterlyAssessment}
                  onChange={(e) => handleQuarterlyAssessmentChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-custom-green focus:border-custom-green"
                  placeholder="0-100"
                />
              </div>
            </div>

            {/* Right Column - Calculation & Summary */}
            <div className="space-y-6">
              {/* Grade Calculation */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Calculator className="w-5 h-5 mr-2" />
                  Grade Calculation
                </h3>
                
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span>Written Works (30%):</span>
                    <span>{(calculateAverage(gradeComponents.writtenWorks) * DEPED_WEIGHTS.writtenWorks).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Performance Tasks (50%):</span>
                    <span>{(calculateAverage(gradeComponents.performanceTasks) * DEPED_WEIGHTS.performanceTasks).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Quarterly Assessment (20%):</span>
                    <span>{(gradeComponents.quarterlyAssessment * DEPED_WEIGHTS.quarterlyAssessment).toFixed(2)}</span>
                  </div>
                  <hr className="border-gray-300" />
                  <div className="flex justify-between font-semibold text-lg">
                    <span>Quarterly Grade:</span>
                    <span className={getGradeColor(computedGrade)}>
                      {computedGrade.toFixed(1)}
                    </span>
                  </div>
                  <div className="text-center">
                    <span className={`font-medium ${getGradeColor(computedGrade)}`}>
                      {getGradeDescriptor(computedGrade)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Validation Errors */}
              {errors.length > 0 && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="font-semibold text-red-900 mb-2 flex items-center">
                    <AlertCircle className="w-4 h-4 mr-2" />
                    Validation Errors
                  </h4>
                  <ul className="text-sm text-red-800 space-y-1">
                    {errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <button
                  onClick={handleSave}
                  disabled={errors.length > 0 || isCalculating}
                  className="flex-1 bg-custom-green text-white px-4 py-2 rounded-md hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                >
                  <Save className="w-4 h-4" />
                  <span>{existingGrade ? 'Update Grade' : 'Save Grade'}</span>
                </button>
                <button
                  onClick={onCancel}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GradeEntry;
