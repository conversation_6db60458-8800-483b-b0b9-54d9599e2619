// Core types for the Grading System

export interface Student {
  id: string;
  name: string;
  grade: number;
  section: string;
  status: 'Active' | 'Inactive';
}

export interface Subject {
  id: string;
  name: string;
  code: string;
}

export interface Section {
  id: string;
  name: string;
  subject: string;
  studentCount: number;
  gradeLevel: number;
}

export interface Grade {
  studentId: string;
  subjectId: string;
  quarter: string;
  score: number;
  maxScore: number;
  dateRecorded: Date;
}

export interface GradeEntry {
  student: Student;
  grades: { [subjectCode: string]: number };
  quarterAverage: number;
}

export interface DashboardStats {
  totalStudents: number;
  gradesPending: number;
}

export interface CalendarEvent {
  id: string;
  title: string;
  date: Date;
  type: 'grade-posting' | 'deadline' | 'event';
}

export type Quarter =
  | '1st Quarter'
  | '2nd Quarter'
  | '3rd Quarter'
  | '4th Quarter';
export type SortOption =
  | 'Name (A-Z)'
  | 'Name (Z-A)'
  | 'Grade (High-Low)'
  | 'Grade (Low-High)';
