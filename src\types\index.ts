// Core types for the Grading System

export interface Student {
  id: string;
  studentId: string;
  name: string;
  grade: number;
  section: string;
  status: 'Active' | 'Inactive';
}

export interface Subject {
  id: string;
  name: string;
  code: string;
}

export interface Section {
  id: string;
  name: string;
  subject: string;
  studentCount: number;
  gradeLevel: number;
}

// DepEd Grading System Components
export interface GradeComponent {
  writtenWorks: number[]; // Array of WW scores
  performanceTasks: number[]; // Array of PT scores
  quarterlyAssessment: number; // Single QA score
}

export interface QuarterlyGrade {
  studentId: string;
  subjectId: string;
  quarter: Quarter;
  components: GradeComponent;
  computedGrade: number; // Computed using DepEd formula
  isComplete: boolean; // Whether all components are recorded
  dateRecorded: Date;
}

export interface StudentGradeRecord {
  studentId: string;
  subjectId: string;
  quarters: {
    '1st Quarter'?: QuarterlyGrade;
    '2nd Quarter'?: QuarterlyGrade;
    '3rd Quarter'?: QuarterlyGrade;
    '4th Quarter'?: QuarterlyGrade;
  };
  finalGrade?: number; // Average of all quarterly grades
  gradeDescriptor?: GradeDescriptor;
}

export interface Teacher {
  id: string;
  name: string;
  email: string;
  assignedSubjects: string[]; // Subject IDs they can teach
  assignedSections: string[]; // Section IDs they teach
}

export type GradeDescriptor =
  | 'Outstanding' // 90-100
  | 'Very Satisfactory' // 85-89
  | 'Satisfactory' // 80-84
  | 'Fairly Satisfactory' // 75-79
  | 'Did Not Meet Expectations'; // Below 75

export interface GradeEntry {
  student: Student;
  grades: { [subjectCode: string]: number };
  quarterAverage: number;
}

export interface DashboardStats {
  totalStudents: number;
  gradesPending: number;
}

export interface CalendarEvent {
  id: string;
  title: string;
  date: Date;
  type: 'grade-posting' | 'deadline' | 'event';
}

export type Quarter =
  | '1st Quarter'
  | '2nd Quarter'
  | '3rd Quarter'
  | '4th Quarter';
export type SortOption =
  | 'Name (A-Z)'
  | 'Name (Z-A)'
  | 'Grade (High-Low)'
  | 'Grade (Low-High)';

// DepEd Grading System Utilities
export interface GradingWeights {
  writtenWorks: number; // 30%
  performanceTasks: number; // 50%
  quarterlyAssessment: number; // 20%
}

export const DEPED_WEIGHTS: GradingWeights = {
  writtenWorks: 0.3,
  performanceTasks: 0.5,
  quarterlyAssessment: 0.2,
};

export const getGradeDescriptor = (grade: number): GradeDescriptor => {
  if (grade >= 90) return 'Outstanding';
  if (grade >= 85) return 'Very Satisfactory';
  if (grade >= 80) return 'Satisfactory';
  if (grade >= 75) return 'Fairly Satisfactory';
  return 'Did Not Meet Expectations';
};

export const calculateQuarterlyGrade = (components: GradeComponent): number => {
  // Calculate average for each component
  const wwAverage =
    components.writtenWorks.length > 0
      ? components.writtenWorks.reduce((sum, score) => sum + score, 0) /
        components.writtenWorks.length
      : 0;

  const ptAverage =
    components.performanceTasks.length > 0
      ? components.performanceTasks.reduce((sum, score) => sum + score, 0) /
        components.performanceTasks.length
      : 0;

  const qaScore = components.quarterlyAssessment || 0;

  // Apply DepEd weights
  const quarterlyGrade =
    wwAverage * DEPED_WEIGHTS.writtenWorks +
    ptAverage * DEPED_WEIGHTS.performanceTasks +
    qaScore * DEPED_WEIGHTS.quarterlyAssessment;

  return Math.round(quarterlyGrade * 100) / 100; // Round to 2 decimal places
};

export const calculateFinalGrade = (quarterlyGrades: number[]): number => {
  if (quarterlyGrades.length === 0) return 0;
  const sum = quarterlyGrades.reduce((total, grade) => total + grade, 0);
  return Math.round((sum / quarterlyGrades.length) * 100) / 100;
};
