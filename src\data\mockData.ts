import type { Student, Subject, Section } from '../types';

export const mockSections: Section[] = [
  {
    id: '1',
    name: 'Grade 7-A',
    subject: 'Mathematics',
    studentCount: 35,
    gradeLevel: 7,
  },
  {
    id: '2',
    name: 'Grade 7-B',
    subject: 'Mathematics',
    studentCount: 32,
    gradeLevel: 7,
  },
  {
    id: '3',
    name: 'Grade 8-A',
    subject: 'Mathematics',
    studentCount: 38,
    gradeLevel: 8,
  },
  {
    id: '4',
    name: 'Grade 8-B',
    subject: 'Mathematics',
    studentCount: 34,
    gradeLevel: 8,
  },
];

export const mockStudents: Student[] = [
  // Grade 7-A Students
  {
    id: '1',
    studentId: '2025001',
    name: '<PERSON>',
    grade: 7,
    section: 'Grade 7-A',
    status: 'Active',
  },
  {
    id: '2',
    studentId: '2025002',
    name: '<PERSON>',
    grade: 7,
    section: 'Grade 7-A',
    status: 'Active',
  },
  {
    id: '3',
    studentId: '2025003',
    name: '<PERSON>',
    grade: 7,
    section: 'Grade 7-A',
    status: 'Active',
  },
  {
    id: '4',
    studentId: '2025004',
    name: '<PERSON>',
    grade: 7,
    section: 'Grade 7-A',
    status: 'Active',
  },
  {
    id: '5',
    studentId: '2025005',
    name: '<PERSON>',
    grade: 7,
    section: 'Grade 7-A',
    status: 'Inactive',
  },

  // Grade 7-B Students
  {
    id: '6',
    studentId: '2025006',
    name: 'Juan Dela Cruz',
    grade: 7,
    section: 'Grade 7-B',
    status: 'Active',
  },
  {
    id: '7',
    studentId: '2025007',
    name: 'Emily Davis',
    grade: 7,
    section: 'Grade 7-B',
    status: 'Active',
  },
  {
    id: '8',
    studentId: '2025008',
    name: 'Chris Miller',
    grade: 7,
    section: 'Grade 7-B',
    status: 'Active',
  },

  // Grade 8-A Students
  {
    id: '9',
    studentId: '2025009',
    name: 'Lisa Garcia',
    grade: 8,
    section: 'Grade 8-A',
    status: 'Active',
  },
  {
    id: '10',
    studentId: '2025010',
    name: 'Tom Anderson',
    grade: 8,
    section: 'Grade 8-A',
    status: 'Active',
  },
  {
    id: '11',
    studentId: '2025011',
    name: 'Amy Taylor',
    grade: 8,
    section: 'Grade 8-A',
    status: 'Active',
  },
  {
    id: '12',
    studentId: '2025012',
    name: 'Robert Chen',
    grade: 8,
    section: 'Grade 8-A',
    status: 'Active',
  },

  // Grade 8-B Students
  {
    id: '13',
    studentId: '2025013',
    name: 'Liam White',
    grade: 8,
    section: 'Grade 8-B',
    status: 'Active',
  },
  {
    id: '14',
    studentId: '2025014',
    name: 'Mia Chen',
    grade: 8,
    section: 'Grade 8-B',
    status: 'Active',
  },
  {
    id: '15',
    studentId: '2025015',
    name: 'Alex Rodriguez',
    grade: 8,
    section: 'Grade 8-B',
    status: 'Active',
  },
  {
    id: '16',
    studentId: '2025016',
    name: 'Sofia Martinez',
    grade: 8,
    section: 'Grade 8-B',
    status: 'Inactive',
  },
];

export const mockSubjects: Subject[] = [
  { id: '1', name: 'Mathematics', code: 'MATH' },
  { id: '2', name: 'Science', code: 'SCI' },
  { id: '3', name: 'English', code: 'ENG' },
  { id: '4', name: 'Social Studies', code: 'SS' },
];

export const mockCalendarEvents = [
  {
    id: '1',
    title: 'Posting Grade',
    date: new Date(2025, 4, 14), // May 14, 2025
    type: 'grade-posting' as const,
  },
  {
    id: '2',
    title: 'Quarter End',
    date: new Date(2025, 4, 30), // May 30, 2025
    type: 'deadline' as const,
  },
];
