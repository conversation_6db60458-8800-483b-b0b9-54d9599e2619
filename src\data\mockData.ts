import type { Student, Subject, Section } from '../types';

export const mockSections: Section[] = [
  {
    id: '1',
    name: 'Grade 7-A',
    subject: 'Mathematics',
    studentCount: 35,
    gradeLevel: 7,
  },
  {
    id: '2',
    name: 'Grade 7-B',
    subject: 'Mathematics',
    studentCount: 32,
    gradeLevel: 7,
  },
  {
    id: '3',
    name: 'Grade 8-A',
    subject: 'Mathematics',
    studentCount: 38,
    gradeLevel: 8,
  },
  {
    id: '4',
    name: 'Grade 8-B',
    subject: 'Mathematics',
    studentCount: 34,
    gradeLevel: 8,
  },
];

export const mockStudents: Student[] = [
  {
    id: '1',
    name: '<PERSON>',
    grade: 7,
    section: 'Grade 7-A',
    status: 'Active',
  },
  {
    id: '2',
    name: '<PERSON>',
    grade: 7,
    section: 'Grade 7-A',
    status: 'Active',
  },
  {
    id: '3',
    name: '<PERSON>',
    grade: 7,
    section: 'Grade 7-A',
    status: 'Active',
  },
  {
    id: '4',
    name: '<PERSON>',
    grade: 7,
    section: 'Grade 7-A',
    status: 'Active',
  },
  {
    id: '5',
    name: '<PERSON>',
    grade: 7,
    section: 'Grade 7-A',
    status: 'Inactive',
  },
  {
    id: '6',
    name: '<PERSON> <PERSON>',
    grade: 7,
    section: 'Grade 7-A',
    status: 'Active',
  },
  {
    id: '7',
    name: '<PERSON> <PERSON>',
    grade: 7,
    section: 'Grade 7-A',
    status: 'Active',
  },
  {
    id: '8',
    name: 'Lisa Garcia',
    grade: 7,
    section: 'Grade 7-A',
    status: 'Active',
  },
  {
    id: '9',
    name: 'Tom <PERSON>',
    grade: 7,
    section: 'Grade 7-A',
    status: 'Active',
  },
  {
    id: '10',
    name: 'Amy Taylor',
    grade: 7,
    section: 'Grade 7-A',
    status: 'Active',
  },
];

export const mockSubjects: Subject[] = [
  { id: '1', name: 'Mathematics', code: 'MATH' },
  { id: '2', name: 'Science', code: 'SCI' },
  { id: '3', name: 'English', code: 'ENG' },
  { id: '4', name: 'Social Studies', code: 'SS' },
];

export const mockCalendarEvents = [
  {
    id: '1',
    title: 'Posting Grade',
    date: new Date(2025, 4, 14), // May 14, 2025
    type: 'grade-posting' as const,
  },
  {
    id: '2',
    title: 'Quarter End',
    date: new Date(2025, 4, 30), // May 30, 2025
    type: 'deadline' as const,
  },
];
