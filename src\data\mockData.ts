import type {
  Student,
  Subject,
  Section,
  Quarter,
  QuarterlyGrade,
  StudentGradeRecord,
} from '../types';

export const mockSections: Section[] = [
  {
    id: '1',
    name: 'Grade 7-A',
    subject: 'Mathematics',
    studentCount: 35,
    gradeLevel: 7,
  },
  {
    id: '2',
    name: 'Grade 7-B',
    subject: 'Mathematics',
    studentCount: 32,
    gradeLevel: 7,
  },
  {
    id: '3',
    name: 'Grade 8-A',
    subject: 'Mathematics',
    studentCount: 38,
    gradeLevel: 8,
  },
  {
    id: '4',
    name: 'Grade 8-B',
    subject: 'Mathematics',
    studentCount: 34,
    gradeLevel: 8,
  },
];

export const mockStudents: Student[] = [
  // Grade 7-A Students
  {
    id: '1',
    studentId: '2025001',
    name: '<PERSON>',
    grade: 7,
    section: 'Grade 7-A',
    status: 'Active',
  },
  {
    id: '2',
    studentId: '2025002',
    name: '<PERSON>',
    grade: 7,
    section: 'Grade 7-A',
    status: 'Active',
  },
  {
    id: '3',
    studentId: '2025003',
    name: '<PERSON>',
    grade: 7,
    section: 'Grade 7-A',
    status: 'Active',
  },
  {
    id: '4',
    studentId: '2025004',
    name: '<PERSON>',
    grade: 7,
    section: 'Grade 7-A',
    status: 'Active',
  },
  {
    id: '5',
    studentId: '2025005',
    name: 'David Brown',
    grade: 7,
    section: 'Grade 7-A',
    status: 'Inactive',
  },

  // Grade 7-B Students
  {
    id: '6',
    studentId: '2025006',
    name: 'Juan Dela Cruz',
    grade: 7,
    section: 'Grade 7-B',
    status: 'Active',
  },
  {
    id: '7',
    studentId: '2025007',
    name: 'Emily Davis',
    grade: 7,
    section: 'Grade 7-B',
    status: 'Active',
  },
  {
    id: '8',
    studentId: '2025008',
    name: 'Chris Miller',
    grade: 7,
    section: 'Grade 7-B',
    status: 'Active',
  },

  // Grade 8-A Students
  {
    id: '9',
    studentId: '2025009',
    name: 'Lisa Garcia',
    grade: 8,
    section: 'Grade 8-A',
    status: 'Active',
  },
  {
    id: '10',
    studentId: '2025010',
    name: 'Tom Anderson',
    grade: 8,
    section: 'Grade 8-A',
    status: 'Active',
  },
  {
    id: '11',
    studentId: '2025011',
    name: 'Amy Taylor',
    grade: 8,
    section: 'Grade 8-A',
    status: 'Active',
  },
  {
    id: '12',
    studentId: '2025012',
    name: 'Robert Chen',
    grade: 8,
    section: 'Grade 8-A',
    status: 'Active',
  },

  // Grade 8-B Students
  {
    id: '13',
    studentId: '2025013',
    name: 'Liam White',
    grade: 8,
    section: 'Grade 8-B',
    status: 'Active',
  },
  {
    id: '14',
    studentId: '2025014',
    name: 'Mia Chen',
    grade: 8,
    section: 'Grade 8-B',
    status: 'Active',
  },
  {
    id: '15',
    studentId: '2025015',
    name: 'Alex Rodriguez',
    grade: 8,
    section: 'Grade 8-B',
    status: 'Active',
  },
  {
    id: '16',
    studentId: '2025016',
    name: 'Sofia Martinez',
    grade: 8,
    section: 'Grade 8-B',
    status: 'Inactive',
  },
];

export const mockSubjects: Subject[] = [
  { id: '1', name: 'Mathematics', code: 'MATH' },
  { id: '2', name: 'Science', code: 'SCI' },
  { id: '3', name: 'English', code: 'ENG' },
  { id: '4', name: 'Filipino', code: 'FIL' },
  { id: '5', name: 'Araling Panlipunan', code: 'AP' },
  { id: '6', name: 'Edukasyon sa Pagpapakatao', code: 'EsP' },
  { id: '7', name: 'MAPEH', code: 'MAPEH' },
  { id: '8', name: 'Technology and Livelihood Education', code: 'TLE' },
];

export const mockCalendarEvents = [
  {
    id: '1',
    title: 'Posting Grade',
    date: new Date(2025, 4, 14), // May 14, 2025
    type: 'grade-posting' as const,
  },
  {
    id: '2',
    title: 'Quarter End',
    date: new Date(2025, 4, 30), // May 30, 2025
    type: 'deadline' as const,
  },
];

// Helper function to create quarterly grades
const createQuarterlyGrade = (
  studentId: string,
  subjectId: string,
  quarter: Quarter,
  grade: number
): QuarterlyGrade => ({
  studentId,
  subjectId,
  quarter,
  components: {
    writtenWorks: [grade * 0.3], // 30% Written Works
    performanceTasks: [grade * 0.5], // 50% Performance Tasks
    quarterlyAssessment: grade * 0.2, // 20% Quarterly Assessment
  },
  computedGrade: grade,
  isComplete: true,
  dateRecorded: new Date(),
});

// Mock grade records for all students
export const mockGradeRecords: StudentGradeRecord[] = [
  // Juan Dela Cruz (2025006) - Mathematics
  {
    studentId: '2025006',
    subjectId: '1',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025006', '1', '1st Quarter', 94),
      '2nd Quarter': createQuarterlyGrade('2025006', '1', '2nd Quarter', 93),
      '3rd Quarter': createQuarterlyGrade('2025006', '1', '3rd Quarter', 95),
      '4th Quarter': createQuarterlyGrade('2025006', '1', '4th Quarter', 94),
    },
    finalGrade: 94,
  },
  // Juan Dela Cruz - Science
  {
    studentId: '2025006',
    subjectId: '2',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025006', '2', '1st Quarter', 92),
      '2nd Quarter': createQuarterlyGrade('2025006', '2', '2nd Quarter', 91),
      '3rd Quarter': createQuarterlyGrade('2025006', '2', '3rd Quarter', 93),
      '4th Quarter': createQuarterlyGrade('2025006', '2', '4th Quarter', 92),
    },
    finalGrade: 92,
  },
  // Juan Dela Cruz - English
  {
    studentId: '2025006',
    subjectId: '3',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025006', '3', '1st Quarter', 90),
      '2nd Quarter': createQuarterlyGrade('2025006', '3', '2nd Quarter', 89),
      '3rd Quarter': createQuarterlyGrade('2025006', '3', '3rd Quarter', 91),
      '4th Quarter': createQuarterlyGrade('2025006', '3', '4th Quarter', 90),
    },
    finalGrade: 90,
  },
  // Juan Dela Cruz - Filipino
  {
    studentId: '2025006',
    subjectId: '4',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025006', '4', '1st Quarter', 89),
      '2nd Quarter': createQuarterlyGrade('2025006', '4', '2nd Quarter', 88),
      '3rd Quarter': createQuarterlyGrade('2025006', '4', '3rd Quarter', 90),
      '4th Quarter': createQuarterlyGrade('2025006', '4', '4th Quarter', 89),
    },
    finalGrade: 89,
  },
  // Juan Dela Cruz - AP
  {
    studentId: '2025006',
    subjectId: '5',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025006', '5', '1st Quarter', 88),
      '2nd Quarter': createQuarterlyGrade('2025006', '5', '2nd Quarter', 87),
      '3rd Quarter': createQuarterlyGrade('2025006', '5', '3rd Quarter', 89),
      '4th Quarter': createQuarterlyGrade('2025006', '5', '4th Quarter', 88),
    },
    finalGrade: 88,
  },
  // Juan Dela Cruz - EsP
  {
    studentId: '2025006',
    subjectId: '6',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025006', '6', '1st Quarter', 91),
      '2nd Quarter': createQuarterlyGrade('2025006', '6', '2nd Quarter', 90),
      '3rd Quarter': createQuarterlyGrade('2025006', '6', '3rd Quarter', 92),
      '4th Quarter': createQuarterlyGrade('2025006', '6', '4th Quarter', 91),
    },
    finalGrade: 91,
  },
  // Juan Dela Cruz - MAPEH
  {
    studentId: '2025006',
    subjectId: '7',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025006', '7', '1st Quarter', 87),
      '2nd Quarter': createQuarterlyGrade('2025006', '7', '2nd Quarter', 86),
      '3rd Quarter': createQuarterlyGrade('2025006', '7', '3rd Quarter', 88),
      '4th Quarter': createQuarterlyGrade('2025006', '7', '4th Quarter', 87),
    },
    finalGrade: 87,
  },
  // Juan Dela Cruz - TLE
  {
    studentId: '2025006',
    subjectId: '8',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025006', '8', '1st Quarter', 93),
      '2nd Quarter': createQuarterlyGrade('2025006', '8', '2nd Quarter', 92),
      '3rd Quarter': createQuarterlyGrade('2025006', '8', '3rd Quarter', 94),
      '4th Quarter': createQuarterlyGrade('2025006', '8', '4th Quarter', 93),
    },
    finalGrade: 93,
  },

  // Chris Miller (2025008) - Mathematics
  {
    studentId: '2025008',
    subjectId: '1',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025008', '1', '1st Quarter', 80),
      '2nd Quarter': createQuarterlyGrade('2025008', '1', '2nd Quarter', 83),
      '3rd Quarter': createQuarterlyGrade('2025008', '1', '3rd Quarter', 81),
      '4th Quarter': createQuarterlyGrade('2025008', '1', '4th Quarter', 82),
    },
    finalGrade: 81.5,
  },
  // Chris Miller - Science
  {
    studentId: '2025008',
    subjectId: '2',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025008', '2', '1st Quarter', 82),
      '2nd Quarter': createQuarterlyGrade('2025008', '2', '2nd Quarter', 84),
      '3rd Quarter': createQuarterlyGrade('2025008', '2', '3rd Quarter', 83),
      '4th Quarter': createQuarterlyGrade('2025008', '2', '4th Quarter', 85),
    },
    finalGrade: 83.5,
  },
  // Chris Miller - English
  {
    studentId: '2025008',
    subjectId: '3',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025008', '3', '1st Quarter', 85),
      '2nd Quarter': createQuarterlyGrade('2025008', '3', '2nd Quarter', 86),
      '3rd Quarter': createQuarterlyGrade('2025008', '3', '3rd Quarter', 84),
      '4th Quarter': createQuarterlyGrade('2025008', '3', '4th Quarter', 87),
    },
    finalGrade: 85.5,
  },
  // Chris Miller - Filipino
  {
    studentId: '2025008',
    subjectId: '4',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025008', '4', '1st Quarter', 83),
      '2nd Quarter': createQuarterlyGrade('2025008', '4', '2nd Quarter', 82),
      '3rd Quarter': createQuarterlyGrade('2025008', '4', '3rd Quarter', 84),
      '4th Quarter': createQuarterlyGrade('2025008', '4', '4th Quarter', 83),
    },
    finalGrade: 83,
  },
  // Chris Miller - AP
  {
    studentId: '2025008',
    subjectId: '5',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025008', '5', '1st Quarter', 81),
      '2nd Quarter': createQuarterlyGrade('2025008', '5', '2nd Quarter', 80),
      '3rd Quarter': createQuarterlyGrade('2025008', '5', '3rd Quarter', 82),
      '4th Quarter': createQuarterlyGrade('2025008', '5', '4th Quarter', 81),
    },
    finalGrade: 81,
  },
  // Chris Miller - EsP
  {
    studentId: '2025008',
    subjectId: '6',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025008', '6', '1st Quarter', 86),
      '2nd Quarter': createQuarterlyGrade('2025008', '6', '2nd Quarter', 85),
      '3rd Quarter': createQuarterlyGrade('2025008', '6', '3rd Quarter', 87),
      '4th Quarter': createQuarterlyGrade('2025008', '6', '4th Quarter', 86),
    },
    finalGrade: 86,
  },
  // Chris Miller - MAPEH
  {
    studentId: '2025008',
    subjectId: '7',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025008', '7', '1st Quarter', 84),
      '2nd Quarter': createQuarterlyGrade('2025008', '7', '2nd Quarter', 83),
      '3rd Quarter': createQuarterlyGrade('2025008', '7', '3rd Quarter', 85),
      '4th Quarter': createQuarterlyGrade('2025008', '7', '4th Quarter', 84),
    },
    finalGrade: 84,
  },
  // Chris Miller - TLE
  {
    studentId: '2025008',
    subjectId: '8',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025008', '8', '1st Quarter', 88),
      '2nd Quarter': createQuarterlyGrade('2025008', '8', '2nd Quarter', 87),
      '3rd Quarter': createQuarterlyGrade('2025008', '8', '3rd Quarter', 89),
      '4th Quarter': createQuarterlyGrade('2025008', '8', '4th Quarter', 88),
    },
    finalGrade: 88,
  },
];

// Helper functions for grade management
export const getStudentGrades = (
  studentId: string,
  subjectId?: string
): StudentGradeRecord[] => {
  return mockGradeRecords.filter(
    (record) =>
      record.studentId === studentId &&
      (subjectId ? record.subjectId === subjectId : true)
  );
};

export const getGradesBySection = (
  sectionName: string,
  subjectId: string
): StudentGradeRecord[] => {
  // This would need to be implemented with student-section mapping
  // For now, return all grades for the subject
  return mockGradeRecords.filter((record) => record.subjectId === subjectId);
};

// Mock teachers data
export const mockTeachers = [
  {
    id: 'teacher-1',
    name: 'Ms. Sarah Johnson',
    email: '<EMAIL>',
    assignedSubjects: ['1'], // Mathematics
    assignedSections: ['1', '2', '3', '4'], // All Mathematics sections
  },
  {
    id: 'teacher-2',
    name: 'Mr. Robert Chen',
    email: '<EMAIL>',
    assignedSubjects: ['1'], // Mathematics
    assignedSections: ['2', '4'], // Grade 7-B, Grade 8-B
  },
];

export const canTeacherEditGrades = (
  teacherId: string,
  subjectId: string,
  sectionId: string
): boolean => {
  const teacher = mockTeachers.find((t) => t.id === teacherId);
  if (!teacher) return false;

  return (
    teacher.assignedSubjects.includes(subjectId) &&
    teacher.assignedSections.includes(sectionId)
  );
};

export const getTeacherPermissions = (teacherId: string) => {
  return mockTeachers.find((t) => t.id === teacherId);
};
