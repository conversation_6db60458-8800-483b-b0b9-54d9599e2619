import type { 
  StudentGradeRecord, 
  QuarterlyGrade, 
  GradeComponent,
  Quarter,
  GradeDescriptor 
} from '../types';
import { 
  calculateQuarterlyGrade, 
  calculateFinalGrade, 
  getGradeDescriptor,
  DEPED_WEIGHTS 
} from '../types';

/**
 * DepEd Grading System Utilities
 * 
 * This file contains utility functions for implementing the official
 * Department of Education (Philippines) grading system with proper
 * calculations and validations.
 */

// Grade validation functions
export const isValidGrade = (grade: number): boolean => {
  return grade >= 0 && grade <= 100;
};

export const validateGradeComponents = (components: GradeComponent): string[] => {
  const errors: string[] = [];
  
  // Validate Written Works
  if (components.writtenWorks.length === 0) {
    errors.push('At least one Written Work score is required');
  }
  components.writtenWorks.forEach((score, index) => {
    if (!isValidGrade(score)) {
      errors.push(`Written Work ${index + 1} must be between 0-100`);
    }
  });
  
  // Validate Performance Tasks
  if (components.performanceTasks.length === 0) {
    errors.push('At least one Performance Task score is required');
  }
  components.performanceTasks.forEach((score, index) => {
    if (!isValidGrade(score)) {
      errors.push(`Performance Task ${index + 1} must be between 0-100`);
    }
  });
  
  // Validate Quarterly Assessment
  if (!isValidGrade(components.quarterlyAssessment)) {
    errors.push('Quarterly Assessment must be between 0-100');
  }
  
  return errors;
};

// Grade calculation with validation
export const calculateQuarterlyGradeWithValidation = (
  components: GradeComponent
): { grade: number; errors: string[] } => {
  const errors = validateGradeComponents(components);
  
  if (errors.length > 0) {
    return { grade: 0, errors };
  }
  
  const grade = calculateQuarterlyGrade(components);
  return { grade, errors: [] };
};

// Get grade status for UI display
export const getGradeStatus = (gradeRecord: StudentGradeRecord, quarter: Quarter): {
  status: 'complete' | 'incomplete' | 'missing';
  grade?: number;
  descriptor?: GradeDescriptor;
} => {
  const quarterGrade = gradeRecord.quarters[quarter];
  
  if (!quarterGrade) {
    return { status: 'missing' };
  }
  
  if (!quarterGrade.isComplete) {
    return { status: 'incomplete' };
  }
  
  return {
    status: 'complete',
    grade: quarterGrade.computedGrade,
    descriptor: getGradeDescriptor(quarterGrade.computedGrade)
  };
};

// Calculate completion percentage for a student
export const getStudentCompletionRate = (gradeRecord: StudentGradeRecord): number => {
  const quarters: Quarter[] = ['1st Quarter', '2nd Quarter', '3rd Quarter', '4th Quarter'];
  const completedQuarters = quarters.filter(quarter => {
    const quarterGrade = gradeRecord.quarters[quarter];
    return quarterGrade && quarterGrade.isComplete;
  }).length;
  
  return (completedQuarters / quarters.length) * 100;
};

// Get class average for a specific quarter
export const getClassAverage = (
  gradeRecords: StudentGradeRecord[], 
  quarter: Quarter
): number => {
  const validGrades = gradeRecords
    .map(record => record.quarters[quarter])
    .filter(quarterGrade => quarterGrade && quarterGrade.isComplete)
    .map(quarterGrade => quarterGrade!.computedGrade);
  
  if (validGrades.length === 0) return 0;
  
  const sum = validGrades.reduce((total, grade) => total + grade, 0);
  return Math.round((sum / validGrades.length) * 100) / 100;
};

// Get grade distribution for analytics
export const getGradeDistribution = (
  gradeRecords: StudentGradeRecord[]
): Record<GradeDescriptor, number> => {
  const distribution: Record<GradeDescriptor, number> = {
    'Outstanding': 0,
    'Very Satisfactory': 0,
    'Satisfactory': 0,
    'Fairly Satisfactory': 0,
    'Did Not Meet Expectations': 0
  };
  
  gradeRecords.forEach(record => {
    if (record.gradeDescriptor) {
      distribution[record.gradeDescriptor]++;
    }
  });
  
  return distribution;
};

// Format grade for display
export const formatGradeForDisplay = (
  grade: number, 
  showDescriptor: boolean = false
): string => {
  const formattedGrade = grade.toFixed(1);
  
  if (!showDescriptor) {
    return formattedGrade;
  }
  
  const descriptor = getGradeDescriptor(grade);
  return `${formattedGrade} (${descriptor})`;
};

// Check if student is at risk (below 75 in any quarter)
export const isStudentAtRisk = (gradeRecord: StudentGradeRecord): boolean => {
  const quarters: Quarter[] = ['1st Quarter', '2nd Quarter', '3rd Quarter', '4th Quarter'];
  
  return quarters.some(quarter => {
    const quarterGrade = gradeRecord.quarters[quarter];
    return quarterGrade && quarterGrade.isComplete && quarterGrade.computedGrade < 75;
  });
};

// Generate grade summary for reports
export const generateGradeSummary = (gradeRecord: StudentGradeRecord) => {
  const quarters: Quarter[] = ['1st Quarter', '2nd Quarter', '3rd Quarter', '4th Quarter'];
  const completedGrades = quarters
    .map(quarter => gradeRecord.quarters[quarter])
    .filter(quarterGrade => quarterGrade && quarterGrade.isComplete)
    .map(quarterGrade => quarterGrade!.computedGrade);
  
  return {
    studentId: gradeRecord.studentId,
    subjectId: gradeRecord.subjectId,
    quarterlyGrades: completedGrades,
    finalGrade: gradeRecord.finalGrade,
    gradeDescriptor: gradeRecord.gradeDescriptor,
    completionRate: getStudentCompletionRate(gradeRecord),
    isAtRisk: isStudentAtRisk(gradeRecord),
    averageGrade: completedGrades.length > 0 
      ? completedGrades.reduce((sum, grade) => sum + grade, 0) / completedGrades.length 
      : 0
  };
};

// Export DepEd weights for reference
export { DEPED_WEIGHTS };

// Grade component weights as percentages for display
export const GRADE_COMPONENT_PERCENTAGES = {
  writtenWorks: 30,
  performanceTasks: 50,
  quarterlyAssessment: 20
};
