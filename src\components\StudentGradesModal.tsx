import React from 'react';
import { X, Download } from 'lucide-react';
import type { Student, Quarter } from '../types';
import { mockGradeRecords, mockSubjects } from '../data/mockData';

interface StudentGradesModalProps {
  student: Student;
  quarter: Quarter;
  onClose: () => void;
}

const StudentGradesModal: React.FC<StudentGradesModalProps> = ({
  student,
  quarter,
  onClose,
}) => {
  // Get all grade records for this student
  const studentGradeRecords = mockGradeRecords.filter(
    (record) => record.studentId === student.studentId
  );

  // Get all subjects and their grades for the current quarter
  const subjectGrades = mockSubjects.map((subject) => {
    const gradeRecord = studentGradeRecords.find(
      (record) => record.subjectId === subject.id
    );
    
    let grade = null;
    let gradeDisplay = '—';
    
    if (gradeRecord) {
      const quarterGrade = gradeRecord.quarters[quarter];
      if (quarterGrade && quarterGrade.isComplete) {
        grade = quarterGrade.computedGrade;
        gradeDisplay = grade.toFixed(1);
      } else if (gradeRecord.finalGrade) {
        grade = gradeRecord.finalGrade;
        gradeDisplay = grade.toFixed(1);
      }
    }

    return {
      subject: subject.name,
      code: subject.code,
      grade,
      gradeDisplay,
    };
  });

  // Calculate quarter average
  const validGrades = subjectGrades
    .filter((sg) => sg.grade !== null)
    .map((sg) => sg.grade!);
  
  const quarterAverage = validGrades.length > 0
    ? validGrades.reduce((sum, grade) => sum + grade, 0) / validGrades.length
    : 0;

  const getGradeColor = (grade: number | null) => {
    if (grade === null) return 'text-gray-500';
    if (grade >= 90) return 'text-green-600';
    if (grade >= 85) return 'text-blue-600';
    if (grade >= 80) return 'text-yellow-600';
    if (grade >= 75) return 'text-orange-600';
    return 'text-red-600';
  };

  const handleExport = () => {
    // In a real app, this would generate and download a PDF or Excel file
    console.log('Exporting grades for:', student.name);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-custom-green text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-bold">{student.name}</h2>
              <p className="text-green-100 text-sm">
                {student.studentId} • {quarter}
              </p>
            </div>
            <button
              onClick={onClose}
              className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-green-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Grades List */}
          <div className="space-y-3 mb-6">
            {subjectGrades.map((subjectGrade) => (
              <div
                key={subjectGrade.code}
                className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0"
              >
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    {subjectGrade.subject}
                  </div>
                  <div className="text-xs text-gray-500">
                    {subjectGrade.code}
                  </div>
                </div>
                <div className={`text-lg font-bold ${getGradeColor(subjectGrade.grade)}`}>
                  {subjectGrade.gradeDisplay}
                </div>
              </div>
            ))}
          </div>

          {/* Quarter Average */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">
                Quarter Average
              </span>
              <span className={`text-xl font-bold ${getGradeColor(quarterAverage)}`}>
                {quarterAverage > 0 ? quarterAverage.toFixed(1) : '—'}
              </span>
            </div>
          </div>

          {/* Export Button */}
          <button
            onClick={handleExport}
            className="w-full flex items-center justify-center space-x-2 bg-custom-green text-white px-4 py-2 rounded-lg hover:opacity-90 transition-opacity"
          >
            <Download className="w-4 h-4" />
            <span>Export</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default StudentGradesModal;
