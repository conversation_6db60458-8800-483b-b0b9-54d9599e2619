import React, { useState } from 'react';
import { ArrowLeft, Users, BookOpen } from 'lucide-react';
import type { Section } from '../types';
import { mockSections, mockStudents } from '../data/mockData';

const MySection: React.FC = () => {
  const [selectedSection, setSelectedSection] = useState<Section | null>(null);

  const sections = mockSections;
  const students = mockStudents.filter((student) =>
    selectedSection ? student.section === selectedSection.name : true
  );

  if (selectedSection) {
    return (
      <div className="p-6 bg-gray-50 min-h-screen">
        {/* Header */}
        <div className="flex items-center space-x-4 mb-6">
          <button
            onClick={() => setSelectedSection(null)}
            className="w-10 h-10 text-white rounded-lg flex items-center justify-center hover:opacity-80 transition-opacity"
            style={{ backgroundColor: '#176D3B' }}
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {selectedSection.name}
            </h1>
            <p className="text-gray-600">
              {selectedSection.subject} • {selectedSection.studentCount}{' '}
              Students
            </p>
          </div>
        </div>

        {/* Students List */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Students</h2>
          </div>
          <div className="divide-y divide-gray-200">
            {students.map((student) => (
              <div
                key={student.id}
                className="px-6 py-4 flex items-center justify-between hover:bg-gray-50"
              >
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                    <Users className="w-5 h-5 text-gray-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{student.name}</p>
                    <p className="text-sm text-gray-600">
                      Grade {student.grade}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <span
                    className={`px-3 py-1 rounded-full text-xs font-medium ${
                      student.status === 'Active'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {student.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">My Sections</h1>
        <p className="text-gray-600">Select a section to view students</p>
      </div>

      {/* Sections Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sections.map((section) => (
          <div
            key={section.id}
            onClick={() => setSelectedSection(section)}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 cursor-pointer hover:shadow-md transition-shadow"
          >
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <BookOpen className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {section.name}
                </h3>
                <p className="text-sm text-gray-600">{section.subject}</p>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Users className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-600">
                  {section.studentCount} Students
                </span>
              </div>
              <button className="text-green-600 hover:text-green-700 text-sm font-medium">
                View →
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MySection;
