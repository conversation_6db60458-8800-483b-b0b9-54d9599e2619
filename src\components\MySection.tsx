import React, { useState } from 'react';
import {
  ArrowLeft,
  Users,
  BookOpen,
  Search,
  ChevronDown,
  Edit3,
} from 'lucide-react';
import type { Section } from '../types';
import {
  mockSections,
  mockStudents,
  mockGradeRecords,
  getStudentGrades,
} from '../data/mockData';
import GradeManagement from './GradeManagement';

const MySection: React.FC = () => {
  const [selectedSection, setSelectedSection] = useState<Section | null>(null);
  const [sortBy, setSortBy] = useState('Name (A-Z)');
  const [searchTerm, setSearchTerm] = useState('');
  const [showGradeManagement, setShowGradeManagement] = useState(false);

  // Get current quarter (this could be dynamic based on current date)
  const getCurrentQuarter = () => {
    const currentMonth = new Date().getMonth() + 1; // 1-12
    if (currentMonth >= 6 && currentMonth <= 8) return '1st Quarter';
    if (currentMonth >= 9 && currentMonth <= 11) return '2nd Quarter';
    if (currentMonth >= 12 || currentMonth <= 2) return '3rd Quarter';
    return '4th Quarter';
  };

  const currentQuarter = getCurrentQuarter();

  const sections = mockSections;

  // Filter students based on selected section
  const filteredStudents = mockStudents.filter((student) =>
    selectedSection ? student.section === selectedSection.name : true
  );

  // Apply search filter
  const searchFilteredStudents = filteredStudents.filter(
    (student) =>
      searchTerm === '' ||
      student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.studentId.includes(searchTerm)
  );

  // Apply sorting
  const sortedStudents = [...searchFilteredStudents].sort((a, b) => {
    switch (sortBy) {
      case 'Name (A-Z)':
        return a.name.localeCompare(b.name);
      case 'Name (Z-A)':
        return b.name.localeCompare(a.name);
      case 'Student ID':
        return a.studentId.localeCompare(b.studentId);
      case 'Grade':
        return a.grade - b.grade;
      default:
        return 0;
    }
  });

  // Get current section info
  const currentSectionInfo = selectedSection || sections[0];

  // Helper function to get current grade for a student
  const getCurrentGrade = (studentId: string): string => {
    const gradeRecord = mockGradeRecords.find(
      (record) => record.studentId === studentId && record.subjectId === '1' // Mathematics
    );

    if (!gradeRecord) return 'No Grade';

    const currentQuarterGrade = gradeRecord.quarters[currentQuarter];
    if (currentQuarterGrade && currentQuarterGrade.isComplete) {
      return currentQuarterGrade.computedGrade.toFixed(1);
    }

    if (gradeRecord.finalGrade) {
      return `${gradeRecord.finalGrade.toFixed(1)} (Final)`;
    }

    return 'In Progress';
  };

  // Show Grade Management if selected
  if (selectedSection && showGradeManagement) {
    return (
      <GradeManagement
        teacherId="teacher-1" // In real app, this would come from auth context
        selectedSection={selectedSection}
        currentQuarter={currentQuarter}
        onBack={() => setShowGradeManagement(false)}
      />
    );
  }

  if (selectedSection) {
    return (
      <div className="p-6 bg-gray-50 min-h-screen">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setSelectedSection(null)}
              className="w-10 h-10 text-white rounded-lg flex items-center justify-center hover:opacity-80 transition-opacity bg-custom-green"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {selectedSection.name}
              </h1>
              <p className="text-gray-600">
                {selectedSection.subject} • {selectedSection.studentCount}{' '}
                Students
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowGradeManagement(true)}
              className="flex items-center space-x-2 bg-custom-green text-white px-4 py-2 rounded-lg hover:opacity-90 transition-opacity"
            >
              <Edit3 className="w-4 h-4" />
              <span>Manage Grades</span>
            </button>
          </div>
        </div>

        {/* Filter Controls */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            {/* Subject */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Subject
              </label>
              <div className="px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-700">
                {currentSectionInfo.subject}
              </div>
            </div>

            {/* Quarter (Read-only) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quarter
              </label>
              <div className="px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-700">
                {currentQuarter}
              </div>
            </div>
          </div>

          {/* Search and Sort */}
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search by name or ID"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-custom-green focus:border-custom-green"
              />
            </div>

            {/* Sort */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700">Sort by</span>
              <div className="relative">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-2 focus:ring-custom-green focus:border-custom-green appearance-none pr-8"
                >
                  <option>Name (A-Z)</option>
                  <option>Name (Z-A)</option>
                  <option>Student ID</option>
                  <option>Grade</option>
                </select>
                <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
              </div>
            </div>
          </div>
        </div>

        {/* Students Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {/* Table Header */}
          <div className="bg-custom-green text-white">
            <div className="grid grid-cols-6 gap-4 px-6 py-3">
              <div className="font-medium text-sm">Name</div>
              <div className="font-medium text-sm">Student ID</div>
              <div className="font-medium text-sm">Grade Level</div>
              <div className="font-medium text-sm">Section</div>
              <div className="font-medium text-sm">Current Grade</div>
              <div className="font-medium text-sm">Status</div>
            </div>
          </div>

          {/* Table Body */}
          <div className="divide-y divide-gray-200">
            {sortedStudents.length === 0 ? (
              <div className="px-6 py-8 text-center text-gray-500">
                No students found
              </div>
            ) : (
              sortedStudents.map((student) => {
                const currentGrade = getCurrentGrade(student.studentId);
                return (
                  <div
                    key={student.id}
                    className="grid grid-cols-6 gap-4 px-6 py-4 hover:bg-gray-50"
                  >
                    <div className="text-sm text-gray-900">{student.name}</div>
                    <div className="text-sm text-gray-600">
                      {student.studentId}
                    </div>
                    <div className="text-sm text-gray-600">{student.grade}</div>
                    <div className="text-sm text-gray-600">
                      {student.section.split('-')[1] || student.section}
                    </div>
                    <div className="text-sm text-gray-600">
                      <span
                        className={`font-medium ${
                          currentGrade === 'No Grade' ||
                          currentGrade === 'In Progress'
                            ? 'text-gray-500'
                            : parseFloat(currentGrade) >= 90
                            ? 'text-green-600'
                            : parseFloat(currentGrade) >= 85
                            ? 'text-blue-600'
                            : parseFloat(currentGrade) >= 80
                            ? 'text-yellow-600'
                            : parseFloat(currentGrade) >= 75
                            ? 'text-orange-600'
                            : 'text-red-600'
                        }`}
                      >
                        {currentGrade}
                      </span>
                    </div>
                    <div>
                      <span
                        className={`px-2 py-1 text-xs font-medium rounded ${
                          student.status === 'Active'
                            ? 'text-green-700 bg-green-100'
                            : 'text-gray-600 bg-gray-100'
                        }`}
                      >
                        {student.status}
                      </span>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">My Sections</h1>
        <p className="text-gray-600">Select a section to view students</p>
      </div>

      {/* Sections Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sections.map((section) => (
          <div
            key={section.id}
            onClick={() => setSelectedSection(section)}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 cursor-pointer hover:shadow-md transition-shadow"
          >
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <BookOpen className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {section.name}
                </h3>
                <p className="text-sm text-gray-600">{section.subject}</p>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Users className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-600">
                  {section.studentCount} Students
                </span>
              </div>
              <button className="text-green-600 hover:text-green-700 text-sm font-medium">
                View →
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MySection;
