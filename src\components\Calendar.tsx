import React, { useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface CalendarEvent {
  date: number;
  title: string;
  type: 'grade-posting' | 'deadline' | 'event';
}

const Calendar: React.FC = () => {
  const [currentDate, setCurrentDate] = useState(new Date(2025, 4)); // May 2025

  const events: CalendarEvent[] = [
    {
      date: 14,
      title: 'Posting Grade',
      type: 'grade-posting',
    },
  ];

  const monthNames = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate((prev) => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  };

  const renderCalendarDays = () => {
    const daysInMonth = getDaysInMonth(currentDate);
    const firstDay = getFirstDayOfMonth(currentDate);
    const days = [];

    // Empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(
        <div
          key={`empty-${i}`}
          className="h-12 border border-gray-300 bg-white"
        ></div>
      );
    }

    // Days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const event = events.find((e) => e.date === day);

      days.push(
        <div
          key={day}
          className="h-12 border border-gray-300 p-1 relative bg-white"
        >
          <span className="text-xs text-gray-700">{day}</span>
          {event && (
            <div className="absolute inset-x-1 bottom-1">
              <div
                className="text-white text-xs px-1 py-0.5 rounded text-center"
                style={{ backgroundColor: '#176D3B' }}
              >
                {event.title}
              </div>
            </div>
          )}
        </div>
      );
    }

    return days;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      {/* Calendar Header */}
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={() => navigateMonth('prev')}
          className="w-7 h-7 text-white rounded flex items-center justify-center hover:opacity-80 transition-opacity"
          style={{ backgroundColor: '#176D3B' }}
        >
          <ChevronLeft className="w-4 h-4" />
        </button>

        <h3 className="text-base font-semibold text-gray-900">
          {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
        </h3>

        <button
          onClick={() => navigateMonth('next')}
          className="w-7 h-7 text-white rounded flex items-center justify-center hover:opacity-80 transition-opacity"
          style={{ backgroundColor: '#176D3B' }}
        >
          <ChevronRight className="w-4 h-4" />
        </button>
      </div>

      {/* Days of Week Header */}
      <div className="grid grid-cols-7 gap-0 mb-1">
        {daysOfWeek.map((day) => (
          <div key={day} className="h-8 flex items-center justify-center">
            <span className="text-xs font-medium text-gray-600">{day}</span>
          </div>
        ))}
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 gap-0 border border-gray-300">
        {renderCalendarDays()}
      </div>
    </div>
  );
};

export default Calendar;
