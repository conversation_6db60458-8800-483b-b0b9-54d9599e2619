import React from 'react';
import {
  LayoutDashboard,
  Users,
  LogOut,
  Menu,
  GraduationCap,
} from 'lucide-react';

interface SidebarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activeTab, onTabChange }) => {
  const menuItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: LayoutDashboard,
    },
    {
      id: 'my-section',
      label: 'My Section',
      icon: Users,
    },
  ];

  return (
    <div className="w-60 min-h-screen flex flex-col bg-custom-green">
      {/* Header */}
      <div className="px-4 py-5 border-b border-green-600">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-white rounded flex items-center justify-center">
              <GraduationCap className="w-4 h-4 text-custom-green" />
            </div>
            <span className="text-white font-medium text-sm">
              Grading System
            </span>
          </div>
          <Menu className="w-4 h-4 text-white" />
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 py-2">
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isActive = activeTab === item.id;

          return (
            <button
              key={item.id}
              onClick={() => onTabChange(item.id)}
              className={`w-full flex items-center space-x-3 px-4 py-3 text-left text-sm transition-colors ${
                isActive
                  ? 'bg-green-600 text-white'
                  : 'text-green-100 hover:bg-green-600 hover:text-white'
              }`}
            >
              <Icon className="w-4 h-4" />
              <span className="font-medium">{item.label}</span>
            </button>
          );
        })}
      </nav>

      {/* Logout */}
      <div className="p-4 border-t border-green-600">
        <button className="w-full flex items-center space-x-3 px-2 py-2 text-green-100 hover:bg-green-600 hover:text-white rounded transition-colors text-sm">
          <LogOut className="w-4 h-4" />
          <span className="font-medium">Logout</span>
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
