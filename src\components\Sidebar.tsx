import React from 'react';
import { LayoutDashboard, Users, LogOut, Menu, GraduationCap } from 'lucide-react';

interface SidebarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activeTab, onTabChange }) => {
  const menuItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: LayoutDashboard,
    },
    {
      id: 'my-section',
      label: 'My Section',
      icon: Users,
    },
  ];

  return (
    <div className="w-64 bg-green-700 min-h-screen flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-green-600">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-white rounded flex items-center justify-center">
            <GraduationCap className="w-5 h-5 text-green-700" />
          </div>
          <span className="text-white font-semibold text-lg">Grading System</span>
          <Menu className="w-5 h-5 text-white ml-auto" />
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 py-4">
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isActive = activeTab === item.id;
          
          return (
            <button
              key={item.id}
              onClick={() => onTabChange(item.id)}
              className={`w-full flex items-center space-x-3 px-6 py-3 text-left transition-colors ${
                isActive
                  ? 'bg-green-600 text-white border-r-4 border-white'
                  : 'text-green-100 hover:bg-green-600 hover:text-white'
              }`}
            >
              <Icon className="w-5 h-5" />
              <span className="font-medium">{item.label}</span>
            </button>
          );
        })}
      </nav>

      {/* Logout */}
      <div className="p-4 border-t border-green-600">
        <button className="w-full flex items-center space-x-3 px-2 py-3 text-green-100 hover:bg-green-600 hover:text-white rounded transition-colors">
          <LogOut className="w-5 h-5" />
          <span className="font-medium">Logout</span>
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
