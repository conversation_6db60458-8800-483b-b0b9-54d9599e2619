import type {
  Teacher,
  StudentGradeRecord,
  QuarterlyGrade,
  GradeComponent,
  Quarter,
} from '../types';
import {
  calculateQuarterlyGrade,
  calculateFinalGrade,
  getGradeDescriptor,
} from '../types';

// Teachers with subject assignments
export const mockTeachers: Teacher[] = [
  {
    id: 'teacher-1',
    name: 'Ms. <PERSON>',
    email: '<EMAIL>',
    assignedSubjects: ['1'], // Mathematics
    assignedSections: ['1', '3'], // Grade 7-A, Grade 8-A
  },
  {
    id: 'teacher-2',
    name: 'Mr. <PERSON>',
    email: '<EMAIL>',
    assignedSubjects: ['1'], // Mathematics
    assignedSections: ['2', '4'], // Grade 7-B, Grade 8-B
  },
  {
    id: 'teacher-3',
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    assignedSubjects: ['2'], // Science
    assignedSections: ['1', '2', '3', '4'], // All sections
  },
  {
    id: 'teacher-4',
    name: '<PERSON><PERSON> <PERSON>',
    email: '<EMAIL>',
    assignedSubjects: ['3'], // English
    assignedSections: ['1', '2', '3', '4'], // All sections
  },
];

// Helper function to generate realistic grade components
const generateGradeComponents = (
  baseScore: number,
  variation: number = 5
): GradeComponent => {
  const randomVariation = () => Math.random() * variation * 2 - variation;

  return {
    writtenWorks: [
      Math.max(0, Math.min(100, baseScore + randomVariation())),
      Math.max(0, Math.min(100, baseScore + randomVariation())),
      Math.max(0, Math.min(100, baseScore + randomVariation())),
    ],
    performanceTasks: [
      Math.max(0, Math.min(100, baseScore + randomVariation())),
      Math.max(0, Math.min(100, baseScore + randomVariation())),
    ],
    quarterlyAssessment: Math.max(
      0,
      Math.min(100, baseScore + randomVariation())
    ),
  };
};

// Helper function to create quarterly grade
const createQuarterlyGrade = (
  studentId: string,
  subjectId: string,
  quarter: Quarter,
  baseScore: number,
  isComplete: boolean = true
): QuarterlyGrade => {
  const components = generateGradeComponents(baseScore);
  const computedGrade = isComplete ? calculateQuarterlyGrade(components) : 0;

  return {
    studentId,
    subjectId,
    quarter,
    components: isComplete
      ? components
      : { writtenWorks: [], performanceTasks: [], quarterlyAssessment: 0 },
    computedGrade,
    isComplete,
    dateRecorded: new Date(),
  };
};

// Sample grade records for students
export const mockGradeRecords: StudentGradeRecord[] = [
  // John Doe - Grade 7-A - Mathematics (Outstanding student)
  {
    studentId: '2025001',
    subjectId: '1',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025001', '1', '1st Quarter', 92),
      '2nd Quarter': createQuarterlyGrade('2025001', '1', '2nd Quarter', 90),
      '3rd Quarter': createQuarterlyGrade('2025001', '1', '3rd Quarter', 94),
      '4th Quarter': createQuarterlyGrade('2025001', '1', '4th Quarter', 91),
    },
    finalGrade: 91.75,
    gradeDescriptor: 'Outstanding',
  },

  // Jane Smith - Grade 7-A - Mathematics (Very Satisfactory)
  {
    studentId: '2025002',
    subjectId: '1',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025002', '1', '1st Quarter', 87),
      '2nd Quarter': createQuarterlyGrade('2025002', '1', '2nd Quarter', 85),
      '3rd Quarter': createQuarterlyGrade('2025002', '1', '3rd Quarter', 88),
      '4th Quarter': createQuarterlyGrade(
        '2025002',
        '1',
        '4th Quarter',
        86,
        false
      ), // Incomplete
    },
    finalGrade: 86.67,
    gradeDescriptor: 'Very Satisfactory',
  },

  // Mike Johnson - Grade 7-A - Mathematics (Satisfactory)
  {
    studentId: '2025003',
    subjectId: '1',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025003', '1', '1st Quarter', 82),
      '2nd Quarter': createQuarterlyGrade('2025003', '1', '2nd Quarter', 80),
      '3rd Quarter': createQuarterlyGrade('2025003', '1', '3rd Quarter', 83),
      // 4th Quarter missing - incomplete record
    },
    finalGrade: 81.67,
    gradeDescriptor: 'Satisfactory',
  },

  // Sarah Wilson - Grade 7-A - Mathematics (Fairly Satisfactory)
  {
    studentId: '2025004',
    subjectId: '1',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025004', '1', '1st Quarter', 77),
      '2nd Quarter': createQuarterlyGrade('2025004', '1', '2nd Quarter', 75),
      '3rd Quarter': createQuarterlyGrade('2025004', '1', '3rd Quarter', 78),
      '4th Quarter': createQuarterlyGrade('2025004', '1', '4th Quarter', 76),
    },
    finalGrade: 76.5,
    gradeDescriptor: 'Fairly Satisfactory',
  },

  // David Brown - Grade 7-A - Mathematics (Did Not Meet Expectations)
  {
    studentId: '2025005',
    subjectId: '1',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025005', '1', '1st Quarter', 72),
      '2nd Quarter': createQuarterlyGrade('2025005', '1', '2nd Quarter', 70),
      // Missing quarters - student struggling
    },
    finalGrade: 71,
    gradeDescriptor: 'Did Not Meet Expectations',
  },

  // Juan Dela Cruz - Grade 7-B - Mathematics (Outstanding)
  {
    studentId: '2025006',
    subjectId: '1',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025006', '1', '1st Quarter', 95),
      '2nd Quarter': createQuarterlyGrade('2025006', '1', '2nd Quarter', 93),
      '3rd Quarter': createQuarterlyGrade('2025006', '1', '3rd Quarter', 96),
      '4th Quarter': createQuarterlyGrade('2025006', '1', '4th Quarter', 94),
    },
    finalGrade: 94.5,
    gradeDescriptor: 'Outstanding',
  },

  // Emily Davis - Grade 7-B - Mathematics (Very Satisfactory)
  {
    studentId: '2025007',
    subjectId: '1',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025007', '1', '1st Quarter', 86),
      '2nd Quarter': createQuarterlyGrade('2025007', '1', '2nd Quarter', 88),
      '3rd Quarter': createQuarterlyGrade('2025007', '1', '3rd Quarter', 87),
      // 4th Quarter in progress
    },
    finalGrade: 87,
    gradeDescriptor: 'Very Satisfactory',
  },

  // Chris Miller - Grade 7-B - Mathematics (Satisfactory)
  {
    studentId: '2025008',
    subjectId: '1',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025008', '1', '1st Quarter', 81),
      '2nd Quarter': createQuarterlyGrade('2025008', '1', '2nd Quarter', 83),
      '3rd Quarter': createQuarterlyGrade('2025008', '1', '3rd Quarter', 80),
      '4th Quarter': createQuarterlyGrade('2025008', '1', '4th Quarter', 82),
    },
    finalGrade: 81.5,
    gradeDescriptor: 'Satisfactory',
  },

  // Lisa Garcia - Grade 8-A - Mathematics (Outstanding)
  {
    studentId: '2025009',
    subjectId: '1',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025009', '1', '1st Quarter', 91),
      '2nd Quarter': createQuarterlyGrade('2025009', '1', '2nd Quarter', 92),
      '3rd Quarter': createQuarterlyGrade('2025009', '1', '3rd Quarter', 90),
      '4th Quarter': createQuarterlyGrade('2025009', '1', '4th Quarter', 93),
    },
    finalGrade: 91.5,
    gradeDescriptor: 'Outstanding',
  },

  // Tom Anderson - Grade 8-A - Mathematics (Very Satisfactory)
  {
    studentId: '2025010',
    subjectId: '1',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025010', '1', '1st Quarter', 85),
      '2nd Quarter': createQuarterlyGrade('2025010', '1', '2nd Quarter', 87),
      '3rd Quarter': createQuarterlyGrade('2025010', '1', '3rd Quarter', 86),
      '4th Quarter': createQuarterlyGrade('2025010', '1', '4th Quarter', 88),
    },
    finalGrade: 86.5,
    gradeDescriptor: 'Very Satisfactory',
  },

  // Amy Taylor - Grade 8-A - Mathematics (Satisfactory)
  {
    studentId: '2025011',
    subjectId: '1',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025011', '1', '1st Quarter', 82),
      '2nd Quarter': createQuarterlyGrade('2025011', '1', '2nd Quarter', 80),
      '3rd Quarter': createQuarterlyGrade('2025011', '1', '3rd Quarter', 84),
      // 4th Quarter pending
    },
    finalGrade: 82,
    gradeDescriptor: 'Satisfactory',
  },

  // Robert Chen - Grade 8-A - Mathematics (Very Satisfactory)
  {
    studentId: '2025012',
    subjectId: '1',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025012', '1', '1st Quarter', 89),
      '2nd Quarter': createQuarterlyGrade('2025012', '1', '2nd Quarter', 87),
      '3rd Quarter': createQuarterlyGrade('2025012', '1', '3rd Quarter', 88),
      '4th Quarter': createQuarterlyGrade('2025012', '1', '4th Quarter', 86),
    },
    finalGrade: 87.5,
    gradeDescriptor: 'Very Satisfactory',
  },

  // Liam White - Grade 8-B - Mathematics (Outstanding)
  {
    studentId: '2025013',
    subjectId: '1',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025013', '1', '1st Quarter', 94),
      '2nd Quarter': createQuarterlyGrade('2025013', '1', '2nd Quarter', 92),
      '3rd Quarter': createQuarterlyGrade('2025013', '1', '3rd Quarter', 95),
      '4th Quarter': createQuarterlyGrade('2025013', '1', '4th Quarter', 93),
    },
    finalGrade: 93.5,
    gradeDescriptor: 'Outstanding',
  },

  // Mia Chen - Grade 8-B - Mathematics (Very Satisfactory)
  {
    studentId: '2025014',
    subjectId: '1',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025014', '1', '1st Quarter', 88),
      '2nd Quarter': createQuarterlyGrade('2025014', '1', '2nd Quarter', 85),
      '3rd Quarter': createQuarterlyGrade('2025014', '1', '3rd Quarter', 87),
      '4th Quarter': createQuarterlyGrade('2025014', '1', '4th Quarter', 89),
    },
    finalGrade: 87.25,
    gradeDescriptor: 'Very Satisfactory',
  },

  // Alex Rodriguez - Grade 8-B - Mathematics (Satisfactory)
  {
    studentId: '2025015',
    subjectId: '1',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025015', '1', '1st Quarter', 83),
      '2nd Quarter': createQuarterlyGrade('2025015', '1', '2nd Quarter', 81),
      '3rd Quarter': createQuarterlyGrade('2025015', '1', '3rd Quarter', 82),
      // 4th Quarter in progress
    },
    finalGrade: 82,
    gradeDescriptor: 'Satisfactory',
  },

  // Sofia Martinez - Grade 8-B - Mathematics (Fairly Satisfactory) - Inactive student
  {
    studentId: '2025016',
    subjectId: '1',
    quarters: {
      '1st Quarter': createQuarterlyGrade('2025016', '1', '1st Quarter', 76),
      '2nd Quarter': createQuarterlyGrade('2025016', '1', '2nd Quarter', 78),
      // Student became inactive - no further records
    },
    finalGrade: 77,
    gradeDescriptor: 'Fairly Satisfactory',
  },
];

// Helper functions for grade management
export const getStudentGrades = (
  studentId: string,
  subjectId?: string
): StudentGradeRecord[] => {
  return mockGradeRecords.filter(
    (record) =>
      record.studentId === studentId &&
      (subjectId ? record.subjectId === subjectId : true)
  );
};

export const getGradesBySection = (
  sectionName: string,
  subjectId: string
): StudentGradeRecord[] => {
  // This would need to be implemented with student-section mapping
  // For now, return all grades for the subject
  return mockGradeRecords.filter((record) => record.subjectId === subjectId);
};

export const canTeacherEditGrades = (
  teacherId: string,
  subjectId: string,
  sectionId: string
): boolean => {
  const teacher = mockTeachers.find((t) => t.id === teacherId);
  if (!teacher) return false;

  return (
    teacher.assignedSubjects.includes(subjectId) &&
    teacher.assignedSections.includes(sectionId)
  );
};

export const getTeacherPermissions = (teacherId: string) => {
  return mockTeachers.find((t) => t.id === teacherId);
};
