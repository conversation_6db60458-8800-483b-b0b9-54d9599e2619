import React from 'react';
import { Users, Clock } from 'lucide-react';
import StatsCard from './StatsCard';
import Calendar from './Calendar';

const Dashboard: React.FC = () => {
  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatsCard
          icon={Users}
          value="102"
          label="Total Students"
          iconColor="bg-green-600"
        />
        <StatsCard
          icon={Clock}
          value="4"
          label="Grades Pending"
          iconColor="bg-green-600"
        />
      </div>

      {/* Calendar */}
      <div className="max-w-4xl">
        <Calendar />
      </div>
    </div>
  );
};

export default Dashboard;
