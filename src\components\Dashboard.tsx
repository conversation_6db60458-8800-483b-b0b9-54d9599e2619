import React from 'react';
import { Users, Clock } from 'lucide-react';
import StatsCard from './StatsCard';
import Calendar from './Calendar';

const Dashboard: React.FC = () => {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-6 max-w-sm">
        <StatsCard icon={Users} value="102" label="Total Students" />
        <StatsCard icon={Clock} value="4" label="Grades Pending" />
      </div>

      {/* Calendar */}
      <div className="max-w-3xl">
        <Calendar />
      </div>
    </div>
  );
};

export default Dashboard;
